# 摸鱼人数统计功能

## 🎯 功能概述

在仪表盘1和仪表盘2中展示当前摸鱼人数，让用户了解有多少同事正在摸鱼，增加产品的社交属性和趣味性。

## ✨ 功能特性

### 1. 实时摸鱼人数统计
- **云端同步**：用户开始/结束摸鱼时自动同步到云端
- **智能过期**：利用工作时间段信息自动清理过期状态
- **实时更新**：每分钟自动更新摸鱼人数

### 2. 仪表盘1统一信息显示
- **动态切换**：摸鱼状态下每5秒在倒计时和摸鱼话术之间切换
- **有趣话术**：支持单人和多人摸鱼的不同话术模板
- **优雅动画**：平滑的淡入淡出切换效果
- **点击交互**：可点击手动切换显示内容

### 3. 仪表盘2简洁展示
- **统计网格**：在统计网格中显示当前摸鱼人数
- **实时更新**：与仪表盘1保持同步更新

## 🎭 摸鱼话术模板

### 单人摸鱼（≤1人）
- "没想到只有自己在摸鱼 🐟"
- "世界那么大，只有我摸鱼 🌍"
- "独自摸鱼的感觉真不错 😌"
- "一个人的摸鱼时光 ⭐"
- "摸鱼界的独行侠 🦸"
- "享受独处的摸鱼时间 🧘"
- "孤独但快乐的摸鱼 🎭"

### 多人摸鱼（>1人）
- "当前共有 {count} 人陪你摸鱼 🐟"
- "全公司有 {count} 位同事在摸鱼中 😎"
- "你不是一个人！还有 {count} 人在摸鱼 🎣"
- "摸鱼大军已达 {count} 人，你们是最棒的！🌊"
- "发现 {count} 位摸鱼高手，继续保持！🏆"
- "当前摸鱼指数：{count} 人在线 📈"
- "摸鱼联盟成员：{count} 人集结完毕 🤝"

## 🔄 交互流程

### 仪表盘1摸鱼状态下的显示流程
```
开始摸鱼 → 立即显示摸鱼话术
    ↓ (5秒后)
自动切换到倒计时
    ↓ (5秒后)
自动切换到摸鱼话术（新话术）
    ↓ (持续循环...)
结束摸鱼 → 停止切换，显示倒计时
```

### 用户交互
- **点击切换**：摸鱼状态下点击文本可手动切换
- **无点击反馈**：点击时无视觉反馈，体验自然
- **动画一致**：所有切换都有平滑的淡入淡出动画

## 🏗️ 技术架构

### 云端数据结构
```javascript
// 集合：fishing_status
{
  userId: "用户ID",
  openid: "用户openid",
  workId: "工作履历ID",
  startTime: "开始摸鱼时间",
  startMinutes: 630, // 开始摸鱼的分钟数
  workSegment: {
    start: 540,
    end: 720,
    type: "work"
  },
  maxEndTime: "最大结束时间",
  remark: "摸鱼备注"
}
```

### 云函数API
- `startFishingStatus` - 开始摸鱼状态记录
- `endFishingStatus` - 结束摸鱼状态记录
- `getCurrentFishingCount` - 获取当前摸鱼人数
- `cleanupExpiredFishingStatus` - 清理过期状态
- `getUserFishingStatus` - 获取用户摸鱼状态

### 前端组件
- **仪表盘1**：统一信息显示，支持动态切换
- **仪表盘2**：统计网格显示
- **摸鱼管理器**：负责状态同步和通知

## ⚡ 性能优化

### 1. 缓存机制
- **30秒缓存**：云函数中缓存摸鱼人数查询结果
- **话术缓存**：前端缓存生成的摸鱼话术，避免频繁生成

### 2. 智能清理
- **自动过期**：利用工作时间段信息自动判断过期
- **批量清理**：每次查询时顺便清理过期数据
- **无需定时任务**：不需要额外的清理定时任务

### 3. 错误处理
- **降级显示**：网络异常时显示"--"
- **功能隔离**：云函数失败不影响本地摸鱼功能
- **静默失败**：同步失败时不打断用户操作

## 🎨 UI设计

### 仪表盘1
- **位置**：底部统一信息显示区域
- **样式**：纯文本，白色阴影，与页面整体风格一致
- **动画**：淡入淡出切换效果

### 仪表盘2
- **位置**：统计网格第一行
- **样式**：统计卡片样式，图标+数值+标签
- **图标**：🐟 摸鱼图标

## 🚀 用户价值

1. **社交属性**：了解同事摸鱼情况，增加归属感
2. **趣味性**：有趣的摸鱼话术增加产品趣味性
3. **实时性**：实时更新的摸鱼人数提供即时反馈
4. **互动性**：可点击切换增加用户参与度

这个功能为产品增加了有趣的社交元素，让摸鱼变得更有趣！🎉
