# 摸鱼人数立即刷新功能

## 🎯 功能需求

用户点击开始摸鱼时，需要立即刷新摸鱼人数，让用户能够马上看到摸鱼人数的变化。

## 🔧 实现方案

### 1. 开始摸鱼时立即刷新
在 `startFishing` 方法中，同步摸鱼状态到云端后立即刷新摸鱼人数：

```javascript
// 同步摸鱼状态到云端
this.syncFishingStatusToCloud('start', {...})

// 立即刷新摸鱼人数
this.refreshFishingCount()

// 通知仪表盘1开始摸鱼
this.notifyFishingStart()
```

### 2. 结束摸鱼时也刷新
在 `endFishing` 方法中，同样在同步状态后立即刷新：

```javascript
// 同步摸鱼状态到云端（结束摸鱼）
this.syncFishingStatusToCloud('end')

// 立即刷新摸鱼人数
this.refreshFishingCount()

// 通知仪表盘1结束摸鱼
this.notifyFishingEnd()
```

### 3. 统一刷新方法
新增 `refreshFishingCount()` 方法，同时刷新仪表盘1和仪表盘2的摸鱼人数：

```javascript
refreshFishingCount() {
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (currentPage && currentPage.selectComponent) {
      // 刷新仪表盘1的摸鱼人数
      const dashboard1 = currentPage.selectComponent('#dashboard1')
      if (dashboard1 && dashboard1.updateFishingCount) {
        dashboard1.updateFishingCount()
      }
      
      // 刷新仪表盘2的摸鱼人数
      const dashboard2 = currentPage.selectComponent('#dashboard2')
      if (dashboard2 && dashboard2.updateFishingCount) {
        dashboard2.updateFishingCount()
      }
    }
  } catch (error) {
    console.warn('刷新摸鱼人数失败:', error)
  }
}
```

## 🎬 执行流程

### 开始摸鱼的完整流程
```
1. 用户点击开始摸鱼
2. 保存本地摸鱼状态
3. 同步摸鱼状态到云端
4. 立即刷新摸鱼人数 ← 新增
5. 通知仪表盘1开始摸鱼
6. 仪表盘1切换到摸鱼话术显示
```

### 结束摸鱼的完整流程
```
1. 用户点击结束摸鱼
2. 清除本地摸鱼状态
3. 同步摸鱼状态到云端
4. 立即刷新摸鱼人数 ← 新增
5. 通知仪表盘1结束摸鱼
6. 仪表盘1停止切换，显示倒计时
```

## ⚡ 用户体验提升

### 修改前
```
开始摸鱼 → 等待1分钟 → 摸鱼人数更新
```

### 修改后
```
开始摸鱼 → 立即更新摸鱼人数 → 用户马上看到变化
```

## 🎯 预期效果

1. **即时反馈**：用户开始摸鱼后立即看到摸鱼人数增加
2. **体验流畅**：不需要等待定时器触发更新
3. **数据一致**：仪表盘1和仪表盘2同时更新
4. **功能完整**：开始和结束摸鱼都有立即反馈

## 🧪 测试验证

### 测试场景1：开始摸鱼
1. 观察当前摸鱼人数（假设为2人）
2. 点击开始摸鱼
3. 立即观察摸鱼人数是否变为3人
4. 确认仪表盘1和仪表盘2都更新了

### 测试场景2：结束摸鱼
1. 在摸鱼状态下观察当前人数（假设为3人）
2. 点击结束摸鱼
3. 立即观察摸鱼人数是否变为2人
4. 确认仪表盘1和仪表盘2都更新了

### 测试场景3：多用户同时操作
1. 多个用户同时开始摸鱼
2. 观察摸鱼人数是否实时更新
3. 验证数据的准确性

## 📋 技术细节

### 组件通信
- 通过 `getCurrentPages()` 获取当前页面
- 通过 `selectComponent()` 获取仪表盘组件
- 调用组件的 `updateFishingCount()` 方法

### 错误处理
- 使用 `try-catch` 包装组件通信
- 失败时输出警告但不影响主流程
- 确保摸鱼功能的核心逻辑不受影响

### 性能考虑
- 立即刷新不会影响云函数的缓存机制
- 只在用户操作时触发，不会增加额外负担
- 异步执行，不阻塞用户界面

这个改进让摸鱼人数的更新更加及时，用户体验更好！🚀
