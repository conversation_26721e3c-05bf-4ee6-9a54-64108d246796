# 摸鱼话术连续切换功能

## 🎯 功能需求

在摸鱼状态下，每5秒在倒计时和摸鱼话术之间反复切换，而不是只切换一次就停止。

## 🔧 修改内容

### 1. 修改切换逻辑
**修改前**：
```javascript
if (this.data.infoDisplayMode === 'fishing') {
  // 从摸鱼话术切换回倒计时
  this.updateUnifiedInfo('countdown')
} else {
  // 当前是倒计时模式，不自动切换到摸鱼话术
  console.log('不自动切换到摸鱼话术')
}
```

**修改后**：
```javascript
if (this.data.infoDisplayMode === 'fishing') {
  // 从摸鱼话术切换回倒计时
  this.updateUnifiedInfo('countdown')
} else {
  // 从倒计时切换到摸鱼话术
  this.setData({
    fishingMessageCache: this.generateFishingMessage()
  })
  this.updateUnifiedInfo('fishing')
}
```

### 2. 移除定时器重置
**修改前**：
- 开始摸鱼时调用 `resetUnifiedInfoTimer()`
- 点击切换时调用 `resetUnifiedInfoTimer()`
- 每次切换都重置5秒定时器

**修改后**：
- 不重置定时器
- 让定时器持续按5秒间隔运行
- 实现连续切换

### 3. 每次切换生成新话术
在定时器切换到摸鱼话术时：
```javascript
// 生成新的摸鱼话术（可选：每次切换时生成新的话术）
this.setData({
  fishingMessageCache: this.generateFishingMessage()
})
```

## 🎬 切换流程

### 摸鱼状态下的完整流程
```
开始摸鱼 → 立即显示摸鱼话术
    ↓ (5秒后)
显示倒计时
    ↓ (5秒后)
显示摸鱼话术 (新话术)
    ↓ (5秒后)
显示倒计时
    ↓ (5秒后)
显示摸鱼话术 (新话术)
    ↓ (持续循环...)
```

### 时间轴示例
```
0秒:  开始摸鱼 → 摸鱼话术A
5秒:  定时器触发 → 倒计时
10秒: 定时器触发 → 摸鱼话术B
15秒: 定时器触发 → 倒计时
20秒: 定时器触发 → 摸鱼话术C
25秒: 定时器触发 → 倒计时
...持续循环
```

## 🧪 测试验证

### 测试场景1：开始摸鱼后的连续切换
1. **0秒**: 点击开始摸鱼
   - 预期：立即显示摸鱼话术
   - 日志：`[Dashboard1] 立即切换到摸鱼话术显示`

2. **5秒**: 第一次定时器触发
   - 预期：切换到倒计时
   - 日志：`[Dashboard1] 定时器触发：从摸鱼话术切换回倒计时`

3. **10秒**: 第二次定时器触发
   - 预期：切换到摸鱼话术（新话术）
   - 日志：`[Dashboard1] 定时器触发：从倒计时切换到摸鱼话术`

4. **15秒**: 第三次定时器触发
   - 预期：切换到倒计时
   - 日志：`[Dashboard1] 定时器触发：从摸鱼话术切换回倒计时`

### 测试场景2：点击切换不影响定时器
1. 在任意时间点击倒计时文本切换到摸鱼话术
2. 定时器仍然按原来的5秒间隔继续运行
3. 不会重置定时器计时

### 测试场景3：结束摸鱼停止切换
1. 点击结束摸鱼
2. 切换回倒计时并停止自动切换
3. 只显示倒计时，不再切换

## 🎯 预期行为

### ✅ 正确的行为
- 摸鱼状态下每5秒自动切换
- 倒计时 ↔ 摸鱼话术 反复循环
- 每次显示摸鱼话术时可以是新的随机话术
- 点击切换不影响定时器节奏

### ❌ 错误的行为（已修复）
- 只切换一次就停止
- 定时器被频繁重置
- 不会从倒计时自动切换到摸鱼话术

## 📋 关键日志

测试时应该看到这样的日志循环：
```
[Dashboard1] 定时器触发：从摸鱼话术切换回倒计时
// 5秒后...
[Dashboard1] 定时器触发：从倒计时切换到摸鱼话术
// 5秒后...
[Dashboard1] 定时器触发：从摸鱼话术切换回倒计时
// 5秒后...
[Dashboard1] 定时器触发：从倒计时切换到摸鱼话术
// 持续循环...
```

现在请测试这个连续切换功能，应该能看到每5秒在倒计时和摸鱼话术之间反复切换！🔄
