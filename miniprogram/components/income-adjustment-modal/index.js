const { formatDateKey } = require('../../utils/helpers/time-utils.js')

/**
 * 收入调整模态框组件
 * 用于添加额外收入和扣款
 */

// 引入服务
const incomeAdjustmentService = require('../../core/services/income-adjustment-service.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      value: false
    },
    // 模式：'income' 或 'deduction'
    mode: {
      type: String,
      value: 'income'
    },
    // 日期字符串 (YYYY-MM-DD 格式)
    dateString: {
      type: String,
      value: ''
    },

    // 编辑模式相关
    isEdit: {
      type: Boolean,
      value: false
    },

    // 编辑的项目数据
    editItem: {
      type: Object,
      value: null
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单数据
    formData: {
      type: '',
      amount: '',
      description: ''
    },

    // 错误信息
    errors: {},

    // 常见类型
    commonTypes: [],

    // 加载状态
    loading: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 安全地获取日期对象
     * @param {any} dateValue - 日期值
     * @returns {Date} 有效的日期对象
     */
    /**
     * 将日期字符串转换为Date对象
     * @param {string} dateString - YYYY-MM-DD 格式的日期字符串
     * @returns {Date} Date对象
     */
    parseDate(dateString) {
      if (!dateString || typeof dateString !== 'string') {
        throw new Error('日期字符串无效')
      }

      // 确保是 YYYY-MM-DD 格式
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/
      if (!dateRegex.test(dateString)) {
        throw new Error('日期格式必须是 YYYY-MM-DD')
      }

      const date = new Date(dateString + 'T00:00:00')
      if (isNaN(date.getTime())) {
        throw new Error('无效的日期')
      }

      return date
    },
    /**
     * 组件初始化
     */
    onLoad() {
      this.updateCommonTypes()
      this.initFormData()
    },

    /**
     * 初始化表单数据
     */
    initFormData() {
      const { isEdit, editItem } = this.data

      let formData = {
        type: '',
        amount: '',
        description: ''
      }

      // 如果是编辑模式，填充现有数据
      if (isEdit && editItem) {
        console.log('编辑模式，填充数据:', editItem)
        formData = {
          type: editItem.type || '',
          amount: editItem.amount ? editItem.amount.toString() : '',
          description: editItem.desc || ''  // 直接使用新的 desc 字段
        }
      }

      this.setData({
        formData,
        errors: {}
      })

      // 编辑模式下不需要设置默认描述
    },

    /**
     * 更新类型选项
     */
    updateCommonTypes() {
      const mode = this.data.mode
      let commonTypes = []

      if (mode === 'income') {
        commonTypes = [
          '销售提成', '绩效奖金', '交通补贴', '餐饮补贴',
          '加班费', '全勤奖', '项目奖金', '年终奖'
        ]
      } else if (mode === 'deduction') {
        commonTypes = [
          '迟到扣款', '早退扣款', '缺勤扣款', '违规罚款',
          '社保扣款', '公积金扣款', '个税扣款', '其他扣款'
        ]
      }

      this.setData({
        commonTypes: commonTypes
      })
    },



    /**
     * 类型输入变化
     */
    onTypeInput(e) {
      const value = e.detail.value
      this.setData({
        'formData.type': value,
        'errors.type': ''  // 清除错误信息
      })
    },

    /**
     * 点击常见类型
     */
    onCommonTypeTap(e) {
      const type = e.currentTarget.dataset.type
      console.log('选择常见类型:', type)
      this.setData({
        'formData.type': type,
        'errors.type': ''  // 清除错误信息
      })
    },

    /**
     * 金额输入
     */
    onAmountInput(e) {
      let value = e.detail.value

      // 限制金额格式：最多两位小数
      if (value) {
        // 只允许数字和一个小数点
        value = value.replace(/[^\d.]/g, '')

        // 确保只有一个小数点
        const parts = value.split('.')
        if (parts.length > 2) {
          value = parts[0] + '.' + parts.slice(1).join('')
        }

        // 限制小数位数为2位
        if (parts.length === 2 && parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }

        // 防止以小数点开头
        if (value.startsWith('.')) {
          value = '0' + value
        }
      }

      this.setData({
        'formData.amount': value,
        'errors.amount': ''
      })
    },

    /**
     * 描述输入
     */
    onDescriptionInput(e) {
      const value = e.detail.value
      this.setData({
        'formData.description': value,
        'errors.description': ''
      })
    },



    /**
     * 验证表单
     */
    validateForm() {
      const { formData } = this.data
      const errors = {}

      // 验证类型
      if (!formData.type || formData.type.trim() === '') {
        errors.type = '请输入类型'
      }

      // 验证金额
      if (!formData.amount || formData.amount.trim() === '') {
        errors.amount = '请输入金额'
      } else {
        const amount = parseFloat(formData.amount)
        if (isNaN(amount) || amount <= 0) {
          errors.amount = '请输入有效的金额'
        }
      }

      this.setData({ errors })
      return Object.keys(errors).length === 0
    },

    /**
     * 确定按钮点击
     */
    async onConfirm() {
      if (!this.validateForm()) {
        return
      }

      this.setData({ loading: true })

      try {
        const { formData, typeOptions, selectedTypeIndex, mode, isEdit, editItem } = this.data
        console.log('模态框确认提交时的数据:', {
          mode,
          isEdit,
          editItem,
          formData
        })

        const type = formData.type ? formData.type.trim() : ''
        const amount = parseFloat(formData.amount)
        const description = formData.description ? formData.description.trim() : ''

        // 验证日期字符串
        const dateString = this.data.dateString
        console.log('模态框收到的日期字符串:', dateString)

        if (!dateString) {
          this.triggerEvent('error', {
            message: '日期无效，请重新选择日期'
          })
          this.setData({ loading: false })
          return
        }

        let date
        try {
          date = this.parseDate(dateString)
          console.log('解析后的日期:', date, '格式化:', formatDateKey(date))
        } catch (error) {
          console.error('日期解析失败:', error)
          this.triggerEvent('error', {
            message: '日期格式错误'
          })
          this.setData({ loading: false })
          return
        }

        let result
        if (isEdit) {
          // 编辑模式：不在这里调用服务，而是返回数据让父组件处理
          console.log('编辑模式，返回表单数据给父组件处理')
          result = true  // 表示表单验证通过
        } else {
          // 添加模式
          if (mode === 'income') {
            result = incomeAdjustmentService.addExtraIncome(
              date,
              type,
              amount,
              description
            )
          } else {
            result = incomeAdjustmentService.addDeduction(
              date,
              type,
              amount,
              description
            )
          }
        }

        console.log(isEdit ? '编辑表单验证成功:' : '添加成功:', result)

        // 准备事件数据
        const eventData = {
          mode: mode,
          type: type,
          amount: amount,
          description: description,  // 保持 description 字段名，用于向后兼容
          desc: description,         // 同时提供 desc 字段名
          id: isEdit ? (editItem ? editItem.id : null) : result,
          isEdit: isEdit,
          editItem: isEdit ? editItem : null  // 在编辑模式下传递完整的 editItem
        }

        console.log('模态框触发成功事件，数据:', eventData)
        console.log('editItem 详细信息:', editItem)

        // 触发成功事件
        this.triggerEvent('success', eventData)

        // 重置表单
        this.resetForm()
        
        // 关闭模态框
        this.onClose()

      } catch (error) {
        console.error('添加失败:', error)
        
        // 触发错误事件
        this.triggerEvent('error', {
          message: error.message || '操作失败'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.setData({
        formData: {
          type: '',
          amount: '',
          description: ''
        },
        errors: {}
      })
    },

    /**
     * 关闭模态框
     */
    onClose() {
      this.resetForm()
      this.triggerEvent('close')
    },

    /**
     * 遮罩层点击
     */
    onOverlayTap() {
      this.onClose()
    },

    /**
     * 模态框内容点击（阻止冒泡）
     */
    onModalTap() {
      // 阻止事件冒泡
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.onLoad()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'mode': function(newMode) {
      this.updateCommonTypes()
      this.initFormData()
    },
    'isEdit, editItem': function(isEdit, editItem) {
      console.log('模态框监听到编辑状态变化:', {
        isEdit,
        editItem,
        editItemType: typeof editItem
      })
      if (this.data.typeOptions && this.data.typeOptions.length > 0) {
        this.initFormData()
      }
    },
    'dateString': function(newDateString) {
      console.log('模态框监听到日期字符串变化:', newDateString)
    }
  }
})
