/* 现代化仪表盘组件样式 */
.dashboard1-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Dashboard1 导航栏 */
.dashboard1-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, rgba(168, 237, 234, 0.95) 0%, rgba(254, 214, 227, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.dashboard1-navbar .status-bar {
  width: 100%;
  background: transparent;
}

.dashboard1-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  position: relative;
}

.dashboard1-navbar .navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16rpx;
  min-width: 144rpx; /* 容纳两个按钮 */
  height: 100%;
}

.dashboard1-navbar .navbar-right {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  height: 100%;
}

.dashboard1-navbar .navbar-right {
  justify-content: flex-end;
}

.dashboard1-navbar .navbar-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard1-navbar .navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.dashboard1-navbar .navbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.dashboard1-navbar .navbar-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.5);
}

.dashboard1-navbar .button-icon {
  font-size: 32rpx;
  color: #2d3748;
}

/* 统一入场动画 */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 背景装饰 */
.dashboard1-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(59, 130, 246, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

/* 引导界面样式 */
.no-work-guide {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  position: relative;
  z-index: 1;
}

.guide-content {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  max-width: 600rpx;
  width: 100%;
}

.guide-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.guide-text {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 48rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.guide-btn {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(76, 81, 191, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.guide-btn:active {
  transform: scale(0.96);
  box-shadow: 0 6rpx 24rpx rgba(76, 81, 191, 0.5);
}

.btn-icon {
  font-size: 28rpx;
}

/* Dashboard1 设置模态框 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.settings-content {
  width: 680rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.settings-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
}

.settings-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.settings-close:active {
  background: #f0f0f0;
}

.settings-body {
  padding: 20rpx 40rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 32rpx;
  color: #2d3748;
  flex: 1;
}

.picker-value {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #2d3748;
  min-width: 120rpx;
  text-align: center;
}

.settings-actions {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 2rpx solid #f0f0f0;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.cancel {
  background: #f8f9fa;
  color: #666666;
}

.action-btn.cancel:active {
  background: #e9ecef;
}

.action-btn.confirm {
  background: #4c51bf;
  color: #ffffff;
}

.action-btn.confirm:active {
  background: #553c9a;
}

/* 主要内容区域样式 */
.main-content {
  padding: 40rpx 32rpx;
  height: calc(100vh - 132rpx); /* 减去导航栏高度 */
  margin-top: 132rpx; /* 导航栏高度 */
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

/* 顶部信息区域 */
.top-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s forwards;
}

.current-time {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.time-text {
  font-size: 48rpx;
  font-weight: 300;
  color: #2d3748;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.date-text {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 400;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.work-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

.work-company,
.work-position {
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.work-company:active,
.work-position:active {
  transform: scale(0.98);
}

.company-text {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 400;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.position-text {
  font-size: 48rpx;
  font-weight: 400;
  color: #2d3748;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 中心切换区域 */
.center-switch-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.switch-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.25s forwards;
}

/* 现代化 Switch 容器 */
.modern-switch-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 现代化 Switch 主体 */
.modern-switch {
  position: relative;
  width: 320rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Switch 背景轨道 */
.switch-track-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 40rpx;
}

/* Switch 选项 */
.switch-option {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.switch-option.left {
  left: 0;
}

.switch-option.right {
  right: 0;
}

.option-icon {
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.option-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #4a5568;
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 激活状态的选项 */
.switch-option.active .option-text {
  color: #ffffff;
  font-weight: 600;
}

.switch-option.active .option-icon {
  transform: scale(1.1);
}

/* 滑动指示器 */
.switch-indicator {
  position: absolute;
  top: 4rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 8rpx);
  border-radius: 36rpx;
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 工作状态指示器 */
.switch-indicator.working {
  left: 4rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* 加班状态指示器 */
.switch-indicator.overtime {
  left: 4rpx;
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
}

/* 摸鱼状态指示器 */
.switch-indicator.fishing {
  left: calc(50% + 4rpx);
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* 加载动画 */
.loading-spinner {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-left-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态描述 */
.switch-description {
  text-align: center;
  margin-top: 16rpx;
  font-size: 24rpx;
}

.description-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.fishing-description {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
}

.fishing-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #f59e0b;
  text-decoration: underline;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fishing-text:active {
  color: #d97706;
  transform: scale(0.95);
}

/* 当前时间段时长显示 */
.segment-duration {
  text-align: center;
  margin-top: 16rpx;
}

.duration-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 1rpx;
  margin-bottom: 4rpx;
}

/* 不同类型的颜色 */
.duration-text.work {
  color: #3b82f6; /* 蓝色 - 工作 */
}

.duration-text.rest {
  color: #10b981; /* 绿色 - 休息 */
}

.duration-text.fishing {
  color: #f59e0b; /* 黄色 - 摸鱼 */
}

.duration-text.overtime {
  color: #ef4444; /* 红色 - 加班 */
}

/* 点击效果 */
.modern-switch:active {
  transform: scale(0.98);
}

/* 实时收入显示 */
.real-time-income {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.15s forwards;
}

.income-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.current-income {
  font-size: 64rpx;
  font-weight: 800;

  background-image: linear-gradient(45deg, #e6b422, #ffa400);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;

  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 1rpx;
}

.income-label {
  font-size: 32rpx;
  color: #4a5568;
  font-weight: 400;
  margin: 12rpx 0 12rpx 0;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.hourly-rate {
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.income-breakdown {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 24rpx;
}

.breakdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.breakdown-label {
  font-size: 20rpx;
  color: #718096;
  font-weight: 400;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.breakdown-value {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.breakdown-value.positive {
  color: #22c55e;
}

.breakdown-value.negative {
  color: #ef4444;
}

.breakdown-separator {
  font-size: 24rpx;
  color: #a0aec0;
  font-weight: 500;
}

.breakdown-item.extra-income .breakdown-label {
  color: #22c55e;
}

.breakdown-item.deductions .breakdown-label {
  color: #ef4444;
}

/* 非工作时间显示 */
.non-work-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.15s forwards;
}

.status-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.status-icon {
  font-size: 80rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  text-align: center;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.countdown {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 32rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  border-radius: 24rpx;
}

.countdown-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #553c9a;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.countdown-label {
  font-size: 22rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}



/* 下一个时间段倒计时 */
.next-segment-countdown {
  display: flex;
  align-self: center;
  align-items: center;
  gap: 12rpx;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.35s forwards;
}

.countdown-header {
  text-align: center;
}

.countdown-prefix {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.countdown-time {
  text-align: center;
}

.time-display {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 结束鼓励话语样式 */
.countdown-finished {
  text-align: center;
}

.encouragement-message {
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border-radius: 16rpx;
  border: 1rpx solid #f59e0b;
}

.encouragement-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #92400e;
  line-height: 1.4;
}

/* 底部统计区域 */
.bottom-stats {
  display: flex;
  flex-direction: column;
  margin-bottom: 64rpx;
  opacity: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.45s forwards;
}

.stat-row {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.stat-item:active {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(0.98);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.stat-label {
  font-size: 22rpx;
  color: #4a5568;
  font-weight: 400;
  text-align: center;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 时间图表区域 */
.time-chart-section {
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.action-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.action-text {
  font-size: 26rpx;
  color: #1a1a1a;
  font-weight: 500;
}

/* 统一信息显示样式 */
.unified-info-section {
  text-align: center;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

.info-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
  line-height: 1.4;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

/* 文本切换动画 */
.info-text.fade-out {
  opacity: 0;
  transform: translateY(-10rpx);
}

.info-text.fade-in {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
