const { formatDateKey } = require('../../utils/helpers/time-utils.js')

/**
 * 时间追踪管理器
 * 负责时间追踪数据的管理和计算
 * 
 * 功能特性：
 * - 时间段的增删改查
 * - 日期数据管理
 * - 收入计算
 * - 工作时长统计
 */

/**
 * 时间追踪管理器类
 */
class TimeTrackingManager {
  constructor() {
    // 数据变化监听器数组
    this.changeListeners = []
  }

  /**
   * 初始化时间追踪数据结构
   * @returns {Object} 初始化的时间追踪对象
   */
  initializeTimeTracking() {
    return {}
  }

  /**
   * 创建新的日期数据
   * @param {Date} date - 日期
   * @returns {Object} 日期数据对象
   */
  createDayData(date) {
    return {
      workDate: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
      status: 'work', // 日期状态：默认为工作日
      segments: [],   // 时间段数组（核心数据）
      fishes: [],     // 摸鱼数据数组
      extraIncomes: [], // 额外收入明细数组
      deductions: [],   // 扣款明细数组
      createTime: new Date()
      // 移除的冗余字段：
      // - dailyIncome: 可通过 segments 计算
      // - hourlyRate: 可通过 segments 计算平均值
      // - totalWorkMinutes: 可通过 segments 计算
      // - netIncome: 可通过 dailyIncome + extraIncomes - deductions 计算
      // - updateTime: 不必要的字段
    }
  }

  /**
   * 生成下一个可用的ID
   * @param {Array} segments - 现有的时间段数组
   * @returns {number} 下一个可用的ID
   */
  generateNextId(segments) {
    if (!Array.isArray(segments) || segments.length === 0) {
      return 0
    }

    const maxId = Math.max(...segments.map(segment => segment.id || 0))
    return maxId + 1
  }

  /**
   * 创建新的时间段
   * @param {number} startMinutes - 开始时间（从当日00:00开始的分钟数）
   * @param {number} endMinutes - 结束时间（从当日00:00开始的分钟数）
   * @param {string} type - 时间段类型
   * @param {number} income - 收入金额
   * @param {number} nextId - 下一个可用的ID（可选，如果不提供则使用0）
   * @returns {Object} 时间段对象
   */
  createTimeSegment(startMinutes, endMinutes, type = 'work', income = 0, nextId = 0) {
    return {
      id: nextId,
      start: startMinutes,
      end: endMinutes,
      type: type,
      income: income
    }
  }

  /**
   * 验证时间段数据
   * @param {Object} segment - 时间段数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
   */
  validateTimeSegment(segment) {
    const errors = []

    if (typeof segment.start !== 'number') {
      errors.push('开始时间必须是数字')
    }

    if (typeof segment.end !== 'number') {
      errors.push('结束时间必须是数字')
    }

    if (typeof segment.start === 'number' && typeof segment.end === 'number') {
      if (segment.end <= segment.start) {
        errors.push('结束时间必须晚于开始时间')
      }

      if (segment.start < 0 || segment.start > 48 * 60) {
        errors.push('开始时间超出有效范围 (0-2880分钟)')
      }

      if (segment.end < 0 || segment.end > 48 * 60) {
        errors.push('结束时间超出有效范围 (0-2880分钟)')
      }
    }

    if (segment.hourlyRate && segment.hourlyRate < 0) {
      errors.push('时薪不能为负数')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * 计算时间段时长（分钟）
   * @param {Object} segment - 时间段对象
   * @returns {number} 时长（分钟）
   */
  calculateSegmentDuration(segment) {
    return Math.max(0, segment.end - segment.start)
  }

  /**
   * 计算时间段收入
   * @param {Object} segment - 时间段对象
   * @returns {number} 收入
   */
  calculateSegmentIncome(segment) {
    const durationMinutes = this.calculateSegmentDuration(segment)
    return (durationMinutes / 60) * (segment.hourlyRate || 0)
  }

  /**
   * 修复浮点数精度问题
   * @param {number} num - 数字
   * @param {number} decimals - 小数位数，默认2位
   * @returns {number} 修复精度后的数字
   */
  fixFloatPrecision(num, decimals = 2) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
  }

  /**
   * 更新日期数据的统计信息（现在只做验证，不再存储计算结果）
   * @param {Object} dayData - 日期数据
   */
  updateDayDataStats(dayData) {
    // 这个方法现在主要用于数据验证和触发重新计算
    // 不再存储可计算的字段到数据中

    // 验证数据结构
    if (!dayData.segments) dayData.segments = []
    if (!dayData.fishes) dayData.fishes = []
    if (!dayData.extraIncomes) dayData.extraIncomes = []
    if (!dayData.deductions) dayData.deductions = []

    // 可以在这里添加数据验证逻辑
    console.log('数据统计更新完成，当前时间段数量:', dayData.segments.length)
  }

  /**
   * 添加时间段到日期数据
   * @param {Object} dayData - 日期数据
   * @param {Object} segment - 时间段
   */
  addSegmentToDayData(dayData, segment) {
    // 验证时间段
    const validation = this.validateTimeSegment(segment)
    if (!validation.isValid) {
      throw new Error('时间段数据验证失败: ' + validation.errors.join(', '))
    }

    // 检查时间冲突
    const hasConflict = this.checkTimeConflict(dayData.segments, segment)
    if (hasConflict) {
      throw new Error('时间段与现有时间段冲突')
    }

    dayData.segments.push(segment)
    this.updateDayDataStats(dayData)
  }

  /**
   * 检查时间冲突
   * @param {Array} existingSegments - 现有时间段数组
   * @param {Object} newSegment - 新时间段
   * @returns {boolean} 是否有冲突
   */
  checkTimeConflict(existingSegments, newSegment) {
    const newStart = newSegment.start
    const newEnd = newSegment.end

    return existingSegments.some(segment => {
      if (segment.id === newSegment.id) {
        return false // 跳过自己
      }

      const existingStart = segment.start
      const existingEnd = segment.end

      // 检查是否有重叠
      return (newStart < existingEnd && newEnd > existingStart)
    })
  }

  /**
   * 更新时间段
   * @param {Object} dayData - 日期数据
   * @param {number} segmentId - 时间段ID
   * @param {Object} updateData - 更新数据
   */
  updateSegment(dayData, segmentId, updateData) {
    const segmentIndex = dayData.segments.findIndex(s => s.id === segmentId)
    if (segmentIndex === -1) {
      throw new Error('时间段不存在')
    }

    const updatedSegment = Object.assign({}, dayData.segments[segmentIndex], updateData)

    // 验证更新后的时间段
    const validation = this.validateTimeSegment(updatedSegment)
    if (!validation.isValid) {
      throw new Error('时间段数据验证失败: ' + validation.errors.join(', '))
    }

    // 检查时间冲突（排除自己）
    const hasConflict = this.checkTimeConflict(dayData.segments, updatedSegment)
    if (hasConflict) {
      throw new Error('时间段与现有时间段冲突')
    }

    dayData.segments[segmentIndex] = updatedSegment
    this.updateDayDataStats(dayData)
  }

  /**
   * 删除时间段
   * @param {Object} dayData - 日期数据
   * @param {number} segmentId - 时间段ID
   */
  removeSegment(dayData, segmentId) {
    const segmentIndex = dayData.segments.findIndex(s => s.id === segmentId)
    if (segmentIndex === -1) {
      throw new Error('时间段不存在')
    }

    dayData.segments.splice(segmentIndex, 1)
    this.updateDayDataStats(dayData)
  }

  /**
   * 获取日期范围内的统计数据
   * @param {Object} timeTracking - 时间追踪数据
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {Object} 统计数据
   */
  getDateRangeStats(timeTracking, startDate, endDate) {
    let totalMinutes = 0
    let totalIncome = 0
    let workDays = 0

    const start = new Date(startDate)
    const end = new Date(endDate)

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dateKey = this.formatDateKey(date)
      const dayData = timeTracking[dateKey]
      
      if (dayData && dayData.segments.length > 0) {
        totalMinutes += dayData.totalWorkMinutes || 0
        totalIncome += dayData.dailyIncome || 0
        workDays++
      }
    }

    return {
      totalMinutes: totalMinutes,
      totalHours: totalMinutes / 60,
      totalIncome: totalIncome,
      workDays: workDays,
      averageHoursPerDay: workDays > 0 ? totalMinutes / 60 / workDays : 0,
      averageIncomePerDay: workDays > 0 ? totalIncome / workDays : 0
    }
  }

  /**
   * 排序时间段
   * @param {Array} segments - 时间段数组
   * @returns {Array} 排序后的时间段数组
   */
  sortSegmentsByTime(segments) {
    if (!Array.isArray(segments)) {
      return []
    }

    return segments.slice().sort((a, b) => {
      // 首先按开始时间排序
      const startDiff = a.start - b.start
      if (startDiff !== 0) {
        return startDiff
      }

      // 如果开始时间相同，按结束时间排序
      return a.end - b.end
    })
  }



  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 要移除的监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 添加摸鱼记录
   * @param {Array} fishes - 现有摸鱼数组
   * @param {Object} fishingData - 摸鱼数据
   * @returns {Array} 更新后的摸鱼数组
   */
  addFishing(fishes, fishingData) {
    if (!Array.isArray(fishes)) {
      fishes = []
    }

    const nextId = this.generateNextId(fishes)
    const newFishing = {
      id: nextId,
      start: fishingData.start,
      end: fishingData.end,
      remark: fishingData.remark || ''
    }

    const updatedFishes = [...fishes, newFishing]

    // 按开始时间排序
    updatedFishes.sort((a, b) => a.start - b.start)

    return updatedFishes
  }

  /**
   * 更新摸鱼记录
   * @param {Array} fishes - 现有摸鱼数组
   * @param {number} fishingId - 摸鱼ID
   * @param {Object} updateData - 更新数据
   * @returns {Array} 更新后的摸鱼数组
   */
  updateFishing(fishes, fishingId, updateData) {
    if (!Array.isArray(fishes)) {
      return []
    }

    const updatedFishes = fishes.map(fish => {
      if (fish.id === fishingId) {
        return {
          ...fish,
          ...updateData,
          id: fishingId // 确保ID不被覆盖
        }
      }
      return fish
    })

    // 按开始时间排序
    updatedFishes.sort((a, b) => a.start - b.start)

    return updatedFishes
  }

  /**
   * 删除摸鱼记录
   * @param {Array} fishes - 现有摸鱼数组
   * @param {number} fishingId - 摸鱼ID
   * @returns {Array} 更新后的摸鱼数组
   */
  deleteFishing(fishes, fishingId) {
    if (!Array.isArray(fishes)) {
      return []
    }

    return fishes.filter(fish => fish.id !== fishingId)
  }

  /**
   * 获取摸鱼记录
   * @param {Array} fishes - 摸鱼数组
   * @param {number} fishingId - 摸鱼ID
   * @returns {Object|null} 摸鱼记录
   */
  getFishing(fishes, fishingId) {
    if (!Array.isArray(fishes)) {
      return null
    }

    return fishes.find(fish => fish.id === fishingId) || null
  }

  /**
   * 验证摸鱼时间段是否与工作时间段冲突
   * @param {Array} segments - 工作时间段数组
   * @param {Object} fishingData - 摸鱼数据
   * @returns {Object} 验证结果
   */
  validateFishingWithSegments(segments, fishingData) {
    if (!Array.isArray(segments) || segments.length === 0) {
      return {
        isValid: false,
        message: '没有工作时间段，无法添加摸鱼记录'
      }
    }

    // 检查摸鱼时间是否完全在某个工作时间段内
    const containingSegment = segments.find(segment => {
      return segment.type !== 'rest' &&
             fishingData.start >= segment.start &&
             fishingData.end <= segment.end
    })

    if (!containingSegment) {
      return {
        isValid: false,
        message: '摸鱼时间必须完全在工作时间段内'
      }
    }

    return {
      isValid: true,
      containingSegment: containingSegment
    }
  }

  // ==================== 额外收入和扣款相关方法 ====================

  // ==================== 动态计算方法 ====================

  /**
   * 计算日期的总工作时长（分钟）
   * @param {Object} dayData - 日期数据
   * @returns {number} 总工作时长（分钟）
   */
  calculateTotalWorkMinutes(dayData) {
    if (!dayData.segments || !Array.isArray(dayData.segments)) {
      return 0
    }

    return dayData.segments.reduce((total, segment) => {
      return total + this.calculateSegmentDuration(segment)
    }, 0)
  }

  /**
   * 计算日期的基础收入
   * @param {Object} dayData - 日期数据
   * @returns {number} 基础收入
   */
  calculateDailyIncome(dayData) {
    if (!dayData.segments || !Array.isArray(dayData.segments)) {
      return 0
    }

    const totalIncome = dayData.segments.reduce((total, segment) => {
      return total + this.calculateSegmentIncome(segment)
    }, 0)

    return this.fixFloatPrecision(totalIncome)
  }

  /**
   * 计算日期的平均时薪
   * @param {Object} dayData - 日期数据
   * @returns {number} 平均时薪
   */
  calculateAverageHourlyRate(dayData) {
    if (!dayData.segments || !Array.isArray(dayData.segments)) {
      return 0
    }

    let totalHourlyRateSum = 0
    let segmentCount = 0

    dayData.segments.forEach(segment => {
      if (segment.hourlyRate > 0) {
        totalHourlyRateSum += segment.hourlyRate
        segmentCount++
      }
    })

    return this.fixFloatPrecision(segmentCount > 0 ? totalHourlyRateSum / segmentCount : 0)
  }

  /**
   * 计算额外收入总额
   * @param {Array} extraIncomes - 额外收入项目数组
   * @returns {number} 额外收入总额
   */
  calculateTotalExtraIncome(extraIncomes) {
    if (!extraIncomes || !Array.isArray(extraIncomes)) {
      return 0
    }

    const total = extraIncomes.reduce((total, item) => total + (item.amount || 0), 0)
    return this.fixFloatPrecision(total)
  }

  /**
   * 计算扣款总额
   * @param {Array} deductions - 扣款项目数组
   * @returns {number} 扣款总额
   */
  calculateTotalDeductions(deductions) {
    if (!deductions || !Array.isArray(deductions)) {
      return 0
    }

    const total = deductions.reduce((total, item) => total + (item.amount || 0), 0)
    return this.fixFloatPrecision(total)
  }

  /**
   * 计算净收入
   * @param {Object} dayData - 日期数据
   * @returns {number} 净收入
   */
  calculateNetIncome(dayData) {
    const dailyIncome = this.calculateDailyIncome(dayData)
    const extraIncome = this.calculateTotalExtraIncome(dayData.extraIncomes || [])
    const deductions = this.calculateTotalDeductions(dayData.deductions || [])

    return this.fixFloatPrecision(dailyIncome + extraIncome - deductions)
  }

  /**
   * 获取日期数据的完整统计信息（动态计算）
   * @param {Object} dayData - 日期数据
   * @returns {Object} 统计信息
   */
  getDayDataStats(dayData) {
    return {
      totalWorkMinutes: this.calculateTotalWorkMinutes(dayData),
      dailyIncome: this.calculateDailyIncome(dayData),
      hourlyRate: this.calculateAverageHourlyRate(dayData),
      extraIncome: this.calculateTotalExtraIncome(dayData.extraIncomes || []),
      deductions: this.calculateTotalDeductions(dayData.deductions || []),
      netIncome: this.calculateNetIncome(dayData)
    }
  }

  /**
   * 创建额外收入项目
   * @param {string} type - 收入类型
   * @param {number} amount - 金额
   * @param {string} description - 描述
   * @param {string} category - 分类（可选）
   * @returns {Object} 额外收入项目对象
   */
  /**
   * 生成下一个可用的ID（数组中最大ID+1）
   * @param {Array} existingItems - 现有项目数组
   * @returns {number} 下一个可用的ID
   */
  generateNextId(existingItems) {
    if (!existingItems || !Array.isArray(existingItems) || existingItems.length === 0) {
      return 0 // 第一个ID从0开始
    }

    // 获取所有现有ID
    const existingIds = existingItems
      .map(item => item.id)
      .filter(id => typeof id === 'number' && !isNaN(id))

    if (existingIds.length === 0) {
      return 0
    }

    // 找到最大ID并+1
    const maxId = Math.max(...existingIds)
    return maxId + 1
  }

  /**
   * 测试ID分配算法（仅用于验证）
   */
  testIdGeneration() {
    console.log('=== 测试ID分配算法（最大值+1） ===')

    // 测试空对象
    console.log('空对象:', this.generateNextId({})) // 应该返回 '0'

    // 测试连续ID
    console.log('连续ID {0:{}, 1:{}, 2:{}}:', this.generateNextId({'0': {}, '1': {}, '2': {}})) // 应该返回 '3'

    // 测试有缺失的ID（不再填补空缺）
    console.log('缺失ID {0:{}, 2:{}, 3:{}}:', this.generateNextId({'0': {}, '2': {}, '3': {}})) // 应该返回 '4'

    // 测试只有一个ID
    console.log('单个ID {0:{}}:', this.generateNextId({'0': {}})) // 应该返回 '1'

    // 测试不从0开始
    console.log('不从0开始 {2:{}, 3:{}}:', this.generateNextId({'2': {}, '3': {}})) // 应该返回 '4'

    // 测试乱序ID
    console.log('乱序ID {5:{}, 1:{}, 3:{}}:', this.generateNextId({'5': {}, '1': {}, '3': {}})) // 应该返回 '6'
  }

  /**
   * 创建额外收入项目
   * @param {number} id - 项目ID
   * @param {string} type - 收入类型
   * @param {number} amount - 金额
   * @param {string} desc - 描述
   * @returns {Object} 额外收入项目
   */
  createExtraIncomeItem(id, type, amount, desc) {
    return {
      id: id,
      type: type,
      desc: desc || '',
      amount: amount
    }
  }

  /**
   * 创建扣款项目
   * @param {number} id - 项目ID
   * @param {string} type - 扣款类型
   * @param {number} amount - 金额
   * @param {string} desc - 描述
   * @returns {Object} 扣款项目
   */
  createDeductionItem(id, type, amount, desc) {
    return {
      id: id,
      type: type,
      desc: desc || '',
      amount: amount
    }
  }

  /**
   * 验证额外收入项目
   * @param {Object} item - 额外收入项目
   * @returns {Object} 验证结果
   */
  validateExtraIncomeItem(item) {
    const errors = []

    if (!item.type || typeof item.type !== 'string') {
      errors.push('收入类型不能为空')
    }

    if (!item.amount || typeof item.amount !== 'number' || item.amount <= 0) {
      errors.push('收入金额必须大于0')
    }

    // 描述字段为可选，只验证类型
    if (item.description !== undefined && typeof item.description !== 'string') {
      errors.push('收入描述必须是字符串类型')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * 验证扣款项目
   * @param {Object} item - 扣款项目
   * @returns {Object} 验证结果
   */
  validateDeductionItem(item) {
    const errors = []

    if (!item.type || typeof item.type !== 'string') {
      errors.push('扣款类型不能为空')
    }

    if (!item.amount || typeof item.amount !== 'number' || item.amount <= 0) {
      errors.push('扣款金额必须大于0')
    }

    // 描述字段为可选，只验证类型
    if (item.description !== undefined && typeof item.description !== 'string') {
      errors.push('扣款描述必须是字符串类型')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * 通知所有监听器数据已变化
   */
  notifyChange() {
    this.changeListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('时间追踪变化监听器执行失败:', error)
      }
    })
  }
}

// 创建并导出单例实例
module.exports = new TimeTrackingManager()
