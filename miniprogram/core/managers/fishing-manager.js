/**
 * 摸鱼管理器
 * 负责摸鱼数据的管理和计算
 * 
 * 功能特性：
 * - 摸鱼记录的增删改查
 * - 摸鱼状态管理（开始/结束）
 * - 摸鱼收入计算
 * - 自动结束摸鱼处理
 * - LocalStorage状态持久化
 */

const { minutesToTimeDisplay, formatDuration } = require('../../utils/helpers/time-utils.js')

/**
 * 摸鱼管理器类
 */
class FishingManager {
  constructor() {
    // 数据变化监听器数组
    this.changeListeners = []

    // LocalStorage键名
    this.FISHING_STATE_KEY = 'fishing_state'
  }

  /**
   * 验证摸鱼数据
   * @param {Object} fishingData - 摸鱼数据
   * @returns {Object} 验证结果 { isValid: boolean, errors: Array }
   */
  validateFishingData(fishingData) {
    const errors = []

    if (!fishingData) {
      errors.push('摸鱼数据不能为空')
      return { isValid: false, errors }
    }

    // 验证开始时间
    if (typeof fishingData.start !== 'number' || fishingData.start < 0) {
      errors.push('开始时间必须是非负数字')
    }

    // 验证结束时间
    if (typeof fishingData.end !== 'number' || fishingData.end < 0) {
      errors.push('结束时间必须是非负数字')
    }

    // 验证时间逻辑
    if (fishingData.start >= fishingData.end) {
      errors.push('结束时间必须大于开始时间')
    }

    // 验证备注长度
    if (fishingData.remark && fishingData.remark.length > 200) {
      errors.push('备注长度不能超过200个字符')
    }

    return { isValid: errors.length === 0, errors }
  }

  /**
   * 验证摸鱼时间段是否在工作时间段内
   * @param {Object} fishingData - 摸鱼数据
   * @param {Array} segments - 工作时间段数组
   * @returns {Object} 验证结果 { isValid: boolean, errors: Array, workSegment: Object }
   */
  validateFishingInWorkTime(fishingData, segments) {
    const errors = []
    let workSegment = null

    if (!segments || segments.length === 0) {
      errors.push('没有工作时间段，无法摸鱼')
      return { isValid: false, errors, workSegment }
    }

    // 查找包含摸鱼时间的工作时间段
    const containingSegments = segments.filter(segment => {
      // 只检查工作和加班时间段
      if (segment.type === 'rest') return false
      
      return fishingData.start >= segment.start && 
             fishingData.end <= segment.end
    })

    if (containingSegments.length === 0) {
      errors.push('摸鱼时间必须完全在工作时间段内')
    } else if (containingSegments.length > 1) {
      errors.push('摸鱼时间跨越了多个工作时间段')
    } else {
      workSegment = containingSegments[0]
    }

    return { 
      isValid: errors.length === 0, 
      errors, 
      workSegment 
    }
  }

  /**
   * 检查摸鱼时间段是否与现有摸鱼记录冲突
   * @param {Object} fishingData - 摸鱼数据
   * @param {Array} existingFishes - 现有摸鱼记录数组
   * @param {number} excludeId - 排除的ID（编辑时使用）
   * @returns {Object} 检查结果 { hasConflict: boolean, conflicts: Array }
   */
  checkFishingConflicts(fishingData, existingFishes, excludeId = null) {
    const conflicts = []

    if (!existingFishes || existingFishes.length === 0) {
      return { hasConflict: false, conflicts }
    }

    existingFishes.forEach(fish => {
      // 排除指定ID（编辑时使用）
      if (excludeId !== null && fish.id === excludeId) {
        return
      }

      // 检查时间重叠
      const hasOverlap = !(fishingData.end <= fish.start || fishingData.start >= fish.end)
      
      if (hasOverlap) {
        conflicts.push({
          id: fish.id,
          start: fish.start,
          end: fish.end,
          startTime: minutesToTimeDisplay(fish.start),
          endTime: minutesToTimeDisplay(fish.end)
        })
      }
    })

    return { 
      hasConflict: conflicts.length > 0, 
      conflicts 
    }
  }

  /**
   * 将摸鱼数据转换为显示格式
   * @param {Object} fishing - 原始摸鱼数据
   * @param {number} hourlyRate - 时薪（用于计算摸鱼收入）
   * @returns {Object} 转换后的摸鱼数据
   */
  convertFishingForDisplay(fishing, hourlyRate = 0) {
    if (!fishing) return fishing

    const duration = fishing.end - fishing.start
    const fishingIncome = hourlyRate > 0 ? (duration / 60) * hourlyRate : 0

    return {
      // 核心数据
      id: fishing.id,
      start: fishing.start,
      end: fishing.end,
      remark: fishing.remark || '',

      // 动态计算的数据
      startTime: minutesToTimeDisplay(fishing.start),
      endTime: minutesToTimeDisplay(fishing.end),
      duration: formatDuration(duration),
      durationMinutes: duration,
      income: fishingIncome
    }
  }

  /**
   * 计算摸鱼总收入
   * @param {Array} fishes - 摸鱼记录数组
   * @param {number} hourlyRate - 时薪
   * @returns {number} 总摸鱼收入
   */
  calculateFishingIncome(fishes, hourlyRate = 0) {
    if (!fishes || fishes.length === 0 || hourlyRate <= 0) {
      return 0
    }

    return fishes.reduce((total, fish) => {
      const duration = fish.end - fish.start
      const fishingIncome = (duration / 60) * hourlyRate
      return total + fishingIncome
    }, 0)
  }

  /**
   * 计算摸鱼总时长（分钟）
   * @param {Array} fishes - 摸鱼记录数组
   * @returns {number} 总摸鱼时长（分钟）
   */
  calculateFishingMinutes(fishes) {
    if (!fishes || fishes.length === 0) {
      return 0
    }

    return fishes.reduce((total, fish) => {
      return total + (fish.end - fish.start)
    }, 0)
  }

  /**
   * 保存摸鱼状态到LocalStorage
   * @param {Object} fishingState - 摸鱼状态
   */
  saveFishingState(fishingState) {
    try {
      wx.setStorageSync(this.FISHING_STATE_KEY, fishingState)
    } catch (error) {
      console.error('保存摸鱼状态失败:', error)
    }
  }

  /**
   * 从LocalStorage获取摸鱼状态
   * @returns {Object|null} 摸鱼状态
   */
  getFishingState() {
    try {
      return wx.getStorageSync(this.FISHING_STATE_KEY) || null
    } catch (error) {
      console.error('获取摸鱼状态失败:', error)
      return null
    }
  }

  /**
   * 清除摸鱼状态
   */
  clearFishingState() {
    try {
      wx.removeStorageSync(this.FISHING_STATE_KEY)
    } catch (error) {
      console.error('清除摸鱼状态失败:', error)
    }
  }

  /**
   * 清理资源（应用退出时调用）
   */
  cleanup() {
    this.changeListeners = []
  }

  /**
   * 开始摸鱼
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {Array} segments - 当日工作时间段
   * @param {string} remark - 备注
   * @returns {Object} 操作结果
   */
  startFishing(workId, date, segments, remark = '') {
    try {
      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      // 检查是否在工作时间内
      const workSegment = segments.find(segment => {
        return segment.type !== 'rest' && 
               currentMinutes >= segment.start && 
               currentMinutes <= segment.end
      })

      if (!workSegment) {
        return {
          success: false,
          message: '当前不在工作时间内，无法开始摸鱼'
        }
      }

      // 保存摸鱼状态
      const fishingState = {
        workId: workId,
        date: date.toISOString().split('T')[0], // YYYY-MM-DD格式
        startTime: now.toISOString(),
        startMinutes: currentMinutes,
        workSegment: workSegment,
        remark: remark,
        isActive: true
      }

      this.saveFishingState(fishingState)

      // 同步摸鱼状态到云端
      this.syncFishingStatusToCloud('start', {
        workId: workId,
        startMinutes: currentMinutes,
        workSegment: workSegment,
        remark: remark
      })

      // 通知仪表盘1开始摸鱼
      this.notifyFishingStart()

      return {
        success: true,
        message: '开始摸鱼',
        fishingState: fishingState
      }
    } catch (error) {
      console.error('开始摸鱼失败:', error)
      return {
        success: false,
        message: '开始摸鱼失败: ' + error.message
      }
    }
  }

  /**
   * 结束摸鱼
   * @param {Function} saveFishingRecord - 保存摸鱼记录的回调函数
   * @returns {Object} 操作结果
   */
  endFishing(saveFishingRecord) {
    try {
      const fishingState = this.getFishingState()
      console.log('当前摸鱼状态:', fishingState)

      if (!fishingState || !fishingState.isActive) {
        console.warn('结束摸鱼失败: 当前没有进行中的摸鱼')
        return {
          success: false,
          message: '当前没有进行中的摸鱼'
        }
      }

      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const durationMinutes = currentMinutes - fishingState.startMinutes
      console.log(`摸鱼时长计算: 当前时间(${currentMinutes}) - 开始时间(${fishingState.startMinutes}) = 时长(${durationMinutes}分钟)`)

      // 创建摸鱼记录
      const fishingRecord = {
        id: 0, // 将由调用方设置正确的ID
        start: fishingState.startMinutes,
        end: currentMinutes,
        remark: fishingState.remark
      }
      console.log('创建的摸鱼记录:', fishingRecord)

      // 总是允许结束摸鱼，但只有时长>=1分钟的才保存记录
      if (durationMinutes >= 1) {
        // 验证摸鱼记录
        const validation = this.validateFishingData(fishingRecord)
        if (!validation.isValid) {
          console.warn('摸鱼记录无效:', validation.errors)
          return {
            success: false,
            message: '摸鱼记录无效: ' + validation.errors.join(', ')
          }
        }

        // 保存摸鱼记录
        if (saveFishingRecord) {
          console.log('保存摸鱼记录:', fishingRecord)
          saveFishingRecord(fishingState.workId, fishingState.date, fishingRecord)
        }
      } else {
        console.log('摸鱼时长不足1分钟，不保存记录')
      }

      // 清除摸鱼状态
      this.clearFishingState()
      console.log('清除摸鱼状态')

      // 同步摸鱼状态到云端（结束摸鱼）
      this.syncFishingStatusToCloud('end')

      // 通知仪表盘1结束摸鱼
      this.notifyFishingEnd()

      return {
        success: true,
        message: '结束摸鱼',
        fishingRecord: durationMinutes >= 1 ? fishingRecord : null,
        duration: formatDuration(durationMinutes),
        saved: durationMinutes >= 1
      }
    } catch (error) {
      console.error('结束摸鱼失败:', error)
      return {
        success: false,
        message: '结束摸鱼失败: ' + error.message
      }
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知数据变化
   */
  notifyChange() {
    this.changeListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('监听器执行失败:', error)
      }
    })
  }

  /**
   * 同步摸鱼状态到云端
   * @param {string} action - 操作类型：'start' 或 'end'
   * @param {Object} data - 摸鱼数据（仅在开始时需要）
   */
  syncFishingStatusToCloud(action, data = {}) {
    const apiType = action === 'start' ? 'startFishingStatus' : 'endFishingStatus'

    return wx.cloud.callFunction({
      name: 'cloud-functions',
      data: {
        type: apiType,
        data: action === 'start' ? data : {}
      }
    }).catch(err => {
      console.warn(`摸鱼状态同步失败，但不影响本地功能: ${action}`, err)
      // 不阻断本地摸鱼功能的正常使用
    })
  }

  /**
   * 通知仪表盘1开始摸鱼
   */
  notifyFishingStart() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      // 查找仪表盘1组件
      if (currentPage && currentPage.selectComponent) {
        const dashboard1 = currentPage.selectComponent('#dashboard1')
        if (dashboard1 && dashboard1.onFishingStart) {
          dashboard1.onFishingStart()
        }
      }
    } catch (error) {
      console.warn('通知仪表盘1开始摸鱼失败:', error)
    }
  }

  /**
   * 通知仪表盘1结束摸鱼
   */
  notifyFishingEnd() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      // 查找仪表盘1组件
      if (currentPage && currentPage.selectComponent) {
        const dashboard1 = currentPage.selectComponent('#dashboard1')
        if (dashboard1 && dashboard1.onFishingEnd) {
          dashboard1.onFishingEnd()
        }
      }
    } catch (error) {
      console.warn('通知仪表盘1结束摸鱼失败:', error)
    }
  }

}

// 创建单例实例
module.exports = new FishingManager()
