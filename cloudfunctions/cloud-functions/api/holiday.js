const { formatDate } = require('../utils/date.js');
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

/**
 * 获取节假日数据
 * 包含当年的所有法定节假日、调休日和周末信息
 */
exports.getHolidayData = async (params = {}) => {
    try {
        const { year = new Date().getFullYear() } = params;
        
        // 生成节假日数据
        const holidayData = generateHolidayData(year);
        
        return {
            success: true,
            data: {
                year: year,
                holidays: holidayData,
                generatedAt: new Date().toISOString(),
                // 缓存有效期：当天23:59:59
                cacheExpiry: new Date(new Date().setHours(23, 59, 59, 999)).toISOString()
            }
        };
    } catch (e) {
        console.error('获取节假日数据失败:', e);
        return {
            success: false,
            errMsg: e.message || '获取节假日数据失败'
        };
    }
};

/**
 * 生成指定年份的节假日数据
 * @param {number} year - 年份
 * @returns {Array} 节假日数据数组
 */
function generateHolidayData(year) {
    const holidays = [];
    
    // 2025年法定节假日数据
    if (year === 2025) {
        // 元旦节
        holidays.push(
            { date: '2025-01-01', name: '元旦', type: 'holiday', isWork: false }
        );
        
        // 春节假期
        const springFestivalDates = ['2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31', '2025-02-01', '2025-02-02', '2025-02-03'];
        springFestivalDates.forEach(date => {
            holidays.push({ date, name: '春节', type: 'holiday', isWork: false });
        });
        
        // 春节调休
        holidays.push(
            { date: '2025-01-26', name: '春节调休', type: 'workday', isWork: true },
            { date: '2025-02-08', name: '春节调休', type: 'workday', isWork: true }
        );
        
        // 清明节
        holidays.push(
            { date: '2025-04-05', name: '清明节', type: 'holiday', isWork: false },
            { date: '2025-04-06', name: '清明节', type: 'holiday', isWork: false },
            { date: '2025-04-07', name: '清明节', type: 'holiday', isWork: false }
        );
        
        // 劳动节
        holidays.push(
            { date: '2025-05-01', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2025-05-02', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2025-05-03', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2025-05-04', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2025-05-05', name: '劳动节', type: 'holiday', isWork: false }
        );
        
        // 劳动节调休
        holidays.push(
            { date: '2025-04-27', name: '劳动节调休', type: 'workday', isWork: true }
        );
        
        // 端午节
        holidays.push(
            { date: '2025-05-31', name: '端午节', type: 'holiday', isWork: false },
            { date: '2025-06-02', name: '端午节', type: 'holiday', isWork: false }
        );
        
        // 中秋节
        holidays.push(
            { date: '2025-10-06', name: '中秋节', type: 'holiday', isWork: false }
        );
        
        // 国庆节
        const nationalDayDates = ['2025-10-01', '2025-10-02', '2025-10-03', '2025-10-04', '2025-10-05', '2025-10-07', '2025-10-08'];
        nationalDayDates.forEach(date => {
            const name = date === '2025-10-06' ? '中秋节' : '国庆节';
            if (date !== '2025-10-06') { // 中秋节已经单独添加
                holidays.push({ date, name: '国庆节', type: 'holiday', isWork: false });
            }
        });
        
        // 国庆节调休
        holidays.push(
            { date: '2025-09-28', name: '国庆节调休', type: 'workday', isWork: true },
            { date: '2025-10-11', name: '国庆节调休', type: 'workday', isWork: true }
        );
    }
    
    // 2024年法定节假日数据（用于跨年展示）
    if (year === 2024) {
        // 元旦节
        holidays.push(
            { date: '2024-01-01', name: '元旦', type: 'holiday', isWork: false }
        );
        
        // 春节假期 
        const springFestivalDates = ['2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', '2024-02-14', '2024-02-15', '2024-02-16', '2024-02-17'];
        springFestivalDates.forEach(date => {
            holidays.push({ date, name: '春节', type: 'holiday', isWork: false });
        });
        
        // 春节调休
        holidays.push(
            { date: '2024-02-04', name: '春节调休', type: 'workday', isWork: true },
            { date: '2024-02-18', name: '春节调休', type: 'workday', isWork: true }
        );
        
        // 清明节
        holidays.push(
            { date: '2024-04-04', name: '清明节', type: 'holiday', isWork: false },
            { date: '2024-04-05', name: '清明节', type: 'holiday', isWork: false },
            { date: '2024-04-06', name: '清明节', type: 'holiday', isWork: false }
        );
        
        // 劳动节
        holidays.push(
            { date: '2024-05-01', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2024-05-02', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2024-05-03', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2024-05-04', name: '劳动节', type: 'holiday', isWork: false },
            { date: '2024-05-05', name: '劳动节', type: 'holiday', isWork: false }
        );
        
        // 劳动节调休
        holidays.push(
            { date: '2024-04-28', name: '劳动节调休', type: 'workday', isWork: true },
            { date: '2024-05-11', name: '劳动节调休', type: 'workday', isWork: true }
        );
        
        // 端午节
        holidays.push(
            { date: '2024-06-08', name: '端午节', type: 'holiday', isWork: false },
            { date: '2024-06-09', name: '端午节', type: 'holiday', isWork: false },
            { date: '2024-06-10', name: '端午节', type: 'holiday', isWork: false }
        );
        
        // 中秋节与国庆节
        const midAutumnNationalDates = ['2024-09-15', '2024-09-16', '2024-09-17', '2024-10-01', '2024-10-02', '2024-10-03', '2024-10-04', '2024-10-05', '2024-10-06', '2024-10-07'];
        midAutumnNationalDates.forEach(date => {
            if (date.startsWith('2024-09-')) {
                holidays.push({ date, name: '中秋节', type: 'holiday', isWork: false });
            } else {
                holidays.push({ date, name: '国庆节', type: 'holiday', isWork: false });
            }
        });
        
        // 中秋国庆调休
        holidays.push(
            { date: '2024-09-14', name: '中秋节调休', type: 'workday', isWork: true },
            { date: '2024-09-29', name: '国庆节调休', type: 'workday', isWork: true },
            { date: '2024-10-12', name: '国庆节调休', type: 'workday', isWork: true }
        );
    }
    
    // 如果是其他年份，可以添加更多年份的数据或使用通用的节假日计算方法
    // 这里为了简化，可以根据需要扩展
    
    return holidays;
}

/**
 * 获取周末信息
 * 为指定年份生成所有周末日期
 */
exports.getWeekendData = async (params = {}) => {
    try {
        const { year = new Date().getFullYear(), month } = params;
        
        const weekends = [];
        const startDate = month ? new Date(year, month - 1, 1) : new Date(year, 0, 1);
        const endDate = month ? new Date(year, month, 0) : new Date(year, 11, 31);
        
        // 遍历指定范围内的所有日期
        for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
            const dayOfWeek = date.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) { // 周日=0, 周六=6
                const dateStr = formatDate(date, 'YYYY-MM-DD');
                weekends.push({
                    date: dateStr,
                    name: dayOfWeek === 0 ? '周日' : '周六',
                    type: 'weekend',
                    isWork: false
                });
            }
        }
        
        return {
            success: true,
            data: {
                year: year,
                month: month,
                weekends: weekends,
                generatedAt: new Date().toISOString()
            }
        };
    } catch (e) {
        console.error('获取周末数据失败:', e);
        return {
            success: false,
            errMsg: e.message || '获取周末数据失败'
        };
    }
}; 