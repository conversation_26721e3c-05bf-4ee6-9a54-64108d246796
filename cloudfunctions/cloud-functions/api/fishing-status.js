/**
 * 摸鱼状态相关API
 */

const fishingStatusDB = require('../db/fishing-status')
const { getCurrentUser } = require('../utils/auth')
const { success, error, wrapAsync, validateRequired } = require('../utils/response')

// 缓存机制
const CACHE_DURATION = 30000 // 30秒缓存
let cachedFishingCount = null
let cacheTime = 0

/**
 * 开始摸鱼状态记录
 */
exports.startFishingStatus = wrapAsync(async (params = {}) => {
  validateRequired(params, ['workId', 'startMinutes', 'workSegment'])

  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 检查是否已有活跃的摸鱼状态
  const hasActiveResult = await fishingStatusDB.hasActiveFishingStatus(user._id)
  if (hasActiveResult.hasActive) {
    // 如果已有状态，先删除旧的再创建新的
    console.log(`用户 ${user._id} 已有摸鱼状态，先清理旧状态`)
    await fishingStatusDB.deleteByUserId(user._id)
  }

  // 验证工作段数据
  const { workSegment } = params
  if (!workSegment || typeof workSegment.start !== 'number' || typeof workSegment.end !== 'number') {
    return error('工作段数据格式错误')
  }

  // 验证开始时间是否在工作段内
  if (params.startMinutes < workSegment.start || params.startMinutes > workSegment.end) {
    return error('摸鱼开始时间不在工作段内')
  }

  // 创建摸鱼状态记录
  const fishingData = {
    userId: user._id,
    openid: user.openid,
    workId: params.workId,
    startMinutes: params.startMinutes,
    workSegment: workSegment,
    remark: params.remark || ''
  }

  const result = await fishingStatusDB.createFishingStatus(fishingData)

  if (!result.success) {
    return result
  }

  // 清除缓存
  cachedFishingCount = null
  cacheTime = 0

  console.log(`用户 ${user._id} 开始摸鱼状态记录成功`)
  return success(null, '摸鱼状态记录成功')
})

/**
 * 结束摸鱼状态记录
 */
exports.endFishingStatus = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 删除用户的摸鱼状态
  const result = await fishingStatusDB.deleteByUserId(user._id)

  if (!result.success) {
    return result
  }

  // 清除缓存
  cachedFishingCount = null
  cacheTime = 0

  console.log(`用户 ${user._id} 结束摸鱼状态记录成功`)
  return success(null, '摸鱼状态清除成功')
})

/**
 * 获取当前摸鱼人数
 */
exports.getCurrentFishingCount = wrapAsync(async (params = {}) => {
  const now = Date.now()
  
  // 如果缓存有效，直接返回缓存结果
  if (cachedFishingCount && (now - cacheTime) < CACHE_DURATION) {
    console.log('返回缓存的摸鱼人数:', cachedFishingCount.data.count)
    return cachedFishingCount
  }

  // 重新计算摸鱼人数
  const result = await fishingStatusDB.getCurrentFishingCount()

  if (!result.success) {
    return result
  }

  // 更新缓存
  const successResult = success(result.data, '获取当前摸鱼人数成功')
  cachedFishingCount = successResult
  cacheTime = now

  console.log(`获取当前摸鱼人数: ${result.data.count}人，清理过期状态: ${result.data.cleanedExpired}个`)
  return successResult
})

/**
 * 清理过期的摸鱼状态（可用于定时任务）
 */
exports.cleanupExpiredFishingStatus = wrapAsync(async (params = {}) => {
  const result = await fishingStatusDB.cleanupExpiredStatus()

  if (!result.success) {
    return result
  }

  // 清除缓存
  cachedFishingCount = null
  cacheTime = 0

  console.log(`清理过期摸鱼状态完成，清理数量: ${result.data.cleanedCount}`)
  return success(result.data, '清理过期摸鱼状态完成')
})

/**
 * 获取用户当前摸鱼状态
 */
exports.getUserFishingStatus = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 查找用户的摸鱼状态
  const result = await fishingStatusDB.findByUserId(user._id)

  if (!result.success) {
    return result
  }

  const hasStatus = result.data !== null
  const statusData = hasStatus ? {
    startTime: result.data.startTime,
    startMinutes: result.data.startMinutes,
    workSegment: result.data.workSegment,
    remark: result.data.remark
  } : null

  return success({
    hasStatus,
    status: statusData
  }, hasStatus ? '用户有活跃的摸鱼状态' : '用户没有活跃的摸鱼状态')
})
