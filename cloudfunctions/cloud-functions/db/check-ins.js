const { formatDate } = require('../utils/date.js')

/**
 * 签到记录数据库操作
 */

const BaseDB = require('./base')

class CheckInsDB extends BaseDB {
  constructor() {
    super('check_ins', { autoTimestamp: false })
    // 添加 db 引用以便在需要时使用
    const cloud = require('wx-server-sdk')
    this.db = cloud.database()
  }

  /**
   * 创建签到记录
   * @param {Object} checkInData - 签到数据
   * @returns {Promise<Object>} 操作结果
   */
  async createCheckIn(checkInData) {
    const defaultData = {
      userId: checkInData.userId,
      date: checkInData.date, // YYYY-MM-DD格式
      checkInAt: checkInData.checkInAt || new Date(),
      consecutiveDays: checkInData.consecutiveDays || 1,
      reward: checkInData.reward || 0,
      ...checkInData
    }

    return await this.create(defaultData)
  }

  /**
   * 检查用户今日是否已签到
   * @param {string} userId - 用户ID
   * @param {string} date - 日期 (YYYY-MM-DD)
   * @returns {Promise<Object>} 检查结果
   */
  async checkTodayCheckIn(userId, date) {
    try {
      const result = await this.find({
        userId,
        date
      })

      return {
        success: true,
        hasCheckedIn: result.success && result.data && result.data.length > 0,
        data: result.data && result.data.length > 0 ? result.data[0] : null
      }
    } catch (error) {
      console.error('检查今日签到状态失败:', error)
      return {
        success: false,
        errMsg: error.message || '检查签到状态失败'
      }
    }
  }

  /**
   * 获取用户签到历史
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 签到历史
   */
  async getUserCheckInHistory(userId, limit = 30) {
    try {
      const result = await this.collection
        .where({ userId })
        .orderBy('checkInAt', 'desc')
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取签到历史失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取签到历史失败'
      }
    }
  }

  /**
   * 获取用户指定月份的签到记录
   * @param {string} userId - 用户ID
   * @param {string} yearMonth - 年月 (YYYY-MM)
   * @returns {Promise<Object>} 月度签到记录
   */
  async getMonthlyCheckIns(userId, yearMonth) {
    try {
      const _ = this.db.command

      const startDate = `${yearMonth}-01`
      const endDate = `${yearMonth}-31` // 简化处理，实际应该计算月末日期

      const result = await this.collection
        .where({
          userId,
          date: _.gte(startDate).and(_.lte(endDate))
        })
        .orderBy('date', 'asc')
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取月度签到记录失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取月度签到记录失败'
      }
    }
  }

  /**
   * 获取用户最近的签到记录
   * @param {string} userId - 用户ID
   * @param {number} days - 天数
   * @returns {Promise<Object>} 最近签到记录
   */
  async getRecentCheckIns(userId, days = 7) {
    try {
      const _ = this.db.command

      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - (days - 1) * 24 * 60 * 60 * 1000)

      const startDateStr = formatDate(startDate, 'YYYY-MM-DD')
      const endDateStr = formatDate(endDate, 'YYYY-MM-DD')

      const result = await this.collection
        .where({
          userId,
          date: _.gte(startDateStr).and(_.lte(endDateStr))
        })
        .orderBy('date', 'desc')
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取最近签到记录失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取最近签到记录失败'
      }
    }
  }

  /**
   * 计算连续签到天数
   * @param {string} userId - 用户ID
   * @param {string} currentDate - 当前日期
   * @returns {Promise<number>} 连续签到天数
   */
  async calculateConsecutiveDays(userId, currentDate) {
    try {
      // 获取最近30天的签到记录
      const result = await this.getUserCheckInHistory(userId, 30)
      if (!result.success || !result.data.length) {
        return 1 // 第一次签到
      }

      const checkIns = result.data.sort((a, b) => new Date(b.date) - new Date(a.date))
      let consecutiveDays = 0
      let checkDate = new Date(currentDate)

      // 从今天开始往前检查
      checkDate.setDate(checkDate.getDate())

      for (const checkIn of checkIns) {
        const checkInDate = checkIn.date
        const expectedDate = formatDate(checkDate, 'YYYY-MM-DD')

        if (checkInDate === expectedDate) {
          consecutiveDays++
          checkDate.setDate(checkDate.getDate() - 1)
        } else {
          break // 中断了连续签到
        }
      }

      return consecutiveDays
    } catch (error) {
      console.error('计算连续签到天数失败:', error)
      return 1
    }
  }

  /**
   * 获取签到统计信息
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 统计信息
   */
  async getCheckInStats(userId) {
    try {
      // 获取总签到天数
      const totalResult = await this.count({ userId })
      const totalDays = totalResult.success ? totalResult.data.total : 0

      // 获取最近签到记录来计算连续天数
      const today = formatDate(new Date(), 'YYYY-MM-DD')
      const todayCheck = await this.checkTodayCheckIn(userId, today)

      let consecutiveDays = 0
      if (todayCheck.hasCheckedIn) {
        consecutiveDays = await this.calculateConsecutiveDays(userId, today)
      } else {
        // 检查昨天是否签到，如果签到了，连续天数就是昨天的连续天数
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        const yesterdayStr = formatDate(yesterday, 'YYYY-MM-DD')
        const yesterdayCheck = await this.checkTodayCheckIn(userId, yesterdayStr)
        
        if (yesterdayCheck.hasCheckedIn) {
          consecutiveDays = yesterdayCheck.data.consecutiveDays
        }
      }

      return {
        success: true,
        data: {
          totalDays,
          consecutiveDays,
          hasCheckedInToday: todayCheck.hasCheckedIn,
          todayCheckIn: todayCheck.data
        }
      }
    } catch (error) {
      console.error('获取签到统计失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取签到统计失败'
      }
    }
  }
}

module.exports = new CheckInsDB()
